#!/usr/bin/env python3
"""
测试失败处理逻辑的脚本
验证文本块没有生成题目时的记录行为
"""

import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from config import Config
from error_handler.error_manager import ErrorManager


def test_failure_handling_with_questions():
    """测试有题目生成时的失败处理"""
    print("测试1: 有题目生成时的失败处理")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建配置
        config = Config()
        config.ERROR_DIR = os.path.join(temp_dir, "err_docs")
        
        # 创建错误管理器
        error_manager = ErrorManager(config)
        
        # 模拟失败的文本块
        failed_chunk = {
            'filename': 'test_doc.txt',
            'chunk_index': 0,
            'content': '这是一个测试文本块，内容不足以生成题目。',
            'token_count': 50,
            'char_count': 25
        }
        
        # 测试有题目生成的情况
        result = error_manager.save_failed_chunk(
            failed_chunk, 
            "内容不足以生成题目", 
            has_generated_questions=True
        )
        
        # 验证结果
        assert result == "", "有题目生成时应该返回空字符串"
        
        # 验证没有创建错误文件
        error_files = os.listdir(config.ERROR_DIR) if os.path.exists(config.ERROR_DIR) else []
        assert len(error_files) == 0, "有题目生成时不应该创建错误文件"
        
        print("OK 测试1通过：有题目生成时只后台输出，不保存文件")


def test_failure_handling_without_questions():
    """测试没有题目生成时的失败处理"""
    print("测试2: 没有题目生成时的失败处理")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建配置
        config = Config()
        config.ERROR_DIR = os.path.join(temp_dir, "err_docs")
        
        # 创建错误管理器
        error_manager = ErrorManager(config)
        
        # 模拟失败的文本块
        failed_chunk = {
            'filename': 'test_doc.txt',
            'chunk_index': 0,
            'content': '这是一个测试文本块，内容不足以生成题目。',
            'token_count': 50,
            'char_count': 25
        }
        
        # 测试没有题目生成的情况
        result = error_manager.save_failed_chunk(
            failed_chunk, 
            "内容不足以生成题目", 
            has_generated_questions=False
        )
        
        # 验证结果
        assert result != "", "没有题目生成时应该返回文件路径"
        assert os.path.exists(result), "应该创建错误文件"
        
        # 验证文件内容
        with open(result, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "test_doc.txt" in content, "文件应包含原文件名"
            assert "内容不足以生成题目" in content, "文件应包含错误信息"
        
        print("OK 测试2通过：没有题目生成时正常保存错误文件")


def test_batch_failure_handling():
    """测试批量失败处理"""
    print("测试3: 批量失败处理")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建配置
        config = Config()
        config.ERROR_DIR = os.path.join(temp_dir, "err_docs")
        
        # 创建错误管理器
        error_manager = ErrorManager(config)
        
        # 模拟多个失败的文本块
        failed_chunks = [
            {
                'filename': 'test_doc1.txt',
                'chunk_index': 0,
                'content': '测试内容1',
                'token_count': 30
            },
            {
                'filename': 'test_doc2.txt',
                'chunk_index': 1,
                'content': '测试内容2',
                'token_count': 40
            }
        ]
        
        # 测试有题目生成的批量处理
        result_files = error_manager.save_failed_chunks_batch(
            failed_chunks,
            error_messages=["错误1", "错误2"],
            has_generated_questions=True
        )
        
        # 验证结果
        assert len(result_files) == 0, "有题目生成时不应该保存任何文件"
        
        # 测试没有题目生成的批量处理
        result_files = error_manager.save_failed_chunks_batch(
            failed_chunks,
            error_messages=["错误1", "错误2"],
            has_generated_questions=False
        )
        
        # 验证结果
        assert len(result_files) == 2, "没有题目生成时应该保存所有失败文件"
        for file_path in result_files:
            assert os.path.exists(file_path), f"文件应该存在: {file_path}"
        
        print("OK 测试3通过：批量处理正确区分有无题目生成的情况")


def test_gui_failure_message_handling():
    """测试GUI失败消息处理逻辑"""
    print("测试4: GUI失败消息处理逻辑")
    
    # 模拟GUI应用
    class MockGUI:
        def __init__(self):
            self.has_generated_questions = False
            self.output_messages = []
        
        def handle_failure_message(self, failure_data):
            filename = failure_data.get('filename', '未知文件')
            chunk_index = failure_data.get('chunk_index', 0)
            reason = failure_data.get('reason', '未知原因')
            chunk_content = failure_data.get('chunk_content', '无内容')
            char_count = len(chunk_content) if chunk_content != '无内容' else 0

            if self.has_generated_questions:
                # 后台输出：只包含文件名、第几个分块、字数、错误原因
                message = f"WARNING 文本块未生成题目 - 文件: {filename}, 分块: {chunk_index+1}, 字数: {char_count}, 原因: {reason}"
                self.output_messages.append(('backend', message))
            else:
                # GUI显示详细信息
                message = f"ERROR 生成失败 - 文件: {filename} (块 {chunk_index})\n原因: {reason}"
                self.output_messages.append(('gui', message))
    
    # 测试没有题目生成时
    gui = MockGUI()
    gui.has_generated_questions = False
    
    failure_data = {
        'filename': 'test.txt',
        'chunk_index': 0,
        'reason': '内容不足',
        'chunk_content': '短内容'
    }
    
    gui.handle_failure_message(failure_data)
    assert len(gui.output_messages) == 1
    assert gui.output_messages[0][0] == 'gui', "没有题目时应该显示在GUI"
    
    # 测试有题目生成时
    gui = MockGUI()
    gui.has_generated_questions = True
    
    gui.handle_failure_message(failure_data)
    assert len(gui.output_messages) == 1
    assert gui.output_messages[0][0] == 'backend', "有题目时应该只后台输出"
    assert "字数: 3" in gui.output_messages[0][1], "应该包含字数信息"
    
    print("OK 测试4通过：GUI正确处理失败消息")


def main():
    """运行所有测试"""
    print("开始测试失败处理逻辑...")
    print("=" * 50)
    
    try:
        test_failure_handling_with_questions()
        print()
        
        test_failure_handling_without_questions()
        print()
        
        test_batch_failure_handling()
        print()
        
        test_gui_failure_message_handling()
        print()
        
        print("=" * 50)
        print("🎉 所有测试通过！失败处理逻辑修改成功")
        print()
        print("修改总结:")
        print("1. ✅ 有题目生成时，失败文本块只在后台输出，不保存文件")
        print("2. ✅ 没有题目生成时，失败文本块正常保存到err_docs文件夹")
        print("3. ✅ 后台输出格式：文件名、分块号、字数、错误原因")
        print("4. ✅ GUI不显示有题目生成后的失败信息")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
